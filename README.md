# ai-members-scheduling-agent

## Development Setup using uv

This project uses [uv](https://github.com/astral-sh/uv), a fast Python package installer and resolver.

1.  **Install uv:**
    ```bash
    curl -LsSf https://astral.sh/uv/install.sh | sh
    ```

2.  **Add uv to your PATH:** (You might need to restart your shell or source your profile file like `.bashrc` or `.zshrc`)
    ```bash
    source $HOME/.cargo/env # Or adjust based on where uv installed its shims
    ```

3.  **Create a virtual environment:**
    ```bash
    uv venv
    ```
    This creates a `.venv` directory.

4.  **Activate the virtual environment:**
    ```bash
    source .venv/bin/activate
    ```

5.  **Install dependencies:**
    You need Nexus credentials exported as environment variables.
    ```bash
    export UV_INDEX_NEXUS_USERNAME=your_nexus_username
    export UV_INDEX_NEXUS_PASSWORD=your_nexus_token
    uv pip install -e ".[dev]"
    ```
    This installs the project in editable mode (`-e .`) along with development dependencies (`[dev]`) defined in `pyproject.toml`.

6.  **Set up pre-commit hooks:**
    ```bash
    pre-commit install
    ```
    This installs the pre-commit hooks defined in `.pre-commit-config.yaml` that will run automatically on each commit to ensure code quality.

## Running Locally

1.  Ensure you have followed the "Development Setup using uv" steps above and your virtual environment is active.
2.  Create a `.env` file with necessary environment variables (copy `.env.example` if it exists).
    Ensure your `.env` file includes:
    - `SCHEDULING_BIOS_API_BASE_URL`: The base URL for the Scheduling Bios API (e.g., `https://qa-api.lifetime.life/scheduling/bios`).
    - `SCHEDULING_BIOS_API_KEY`: The API key for authenticating with the Scheduling Bios API.
3.  Run the application:
    ```bash
    uv run uvicorn app.main:app --reload --port 8000
    ```
    Or using the gunicorn command specified in the Dockerfile (without ddtrace-run locally unless datadog is setup):
    ```bash
    uv run gunicorn -k uvicorn.workers.UvicornWorker -w 4 --threads 1 --bind 0.0.0.0:8000 app.main:app
    ```

## Build and Run via Docker

The `Dockerfile` uses a multi-stage build process. It installs `uv` and then uses `uv pip install` to manage dependencies based on `pyproject.toml` (and `uv.lock` if present).

### Setup Secrets for Build

Assumes you already have `NEXUS_AUTH_USER` and `NEXUS_AUTH_TOKEN` exported into the shell as env vars.

Create a temporary secrets directory (this mimics how GitHub Actions handles secrets):
```bash
mkdir -p .secrets
echo "$NEXUS_AUTH_USER" > .secrets/NEXUS_AUTH_USER
echo "$NEXUS_AUTH_TOKEN" > .secrets/NEXUS_AUTH_TOKEN
