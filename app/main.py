import json
import logging
import os
import traceback
from typing import AsyncGenerator

import dotenv
from ai_agent_utils.graph.dispatcher import function_dispatch
from ai_agent_utils.graph.dispatcher_function_graph import initialize_function_graph
from ai_agent_utils.logging import initialize_logger, logger
from ai_agent_utils.services.kafka import Kafka<PERSON><PERSON>
from ai_agent_utils.services.launch_darkly import LaunchDarklyClient
from ai_agent_utils.services.mongo import MongoDBClient
from ai_agent_utils.services.openai import OpenAIClient
from ai_agent_utils.services.redis import Redis
from fastapi import FastAPI, Header, HTTPException
from fastapi.concurrency import asynccontextmanager
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse, StreamingResponse

from app.function_graph import FUNCTION_GRAPH
from app.handlers.starting_handler import starting_handler
from app.validations.chats import ChatRequest

dotenv.load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Code to run before the app starts
    initialize_logger("ai-members-scheduling-agent", level=logging.INFO)
    logger.info("Application starting", facets={"event": "startup"})

    initialize_function_graph(FUNCTION_GRAPH)
    await Redis.connect(
        host=os.getenv("REDIS_HOST"),
        password=os.getenv("REDIS_PASSWORD"),
        port=int(os.getenv("REDIS_PORT")),
    )
    await OpenAIClient.connect()
    MongoDBClient.connect(connection_string=os.getenv("MONGO_CONNECTION_STRING"))
    KafkaClient.kafka_conf(
        client_id="ai-members-scheduling-agent",
        brokers=os.getenv("KAFKA_BROKERS"),
        username=os.getenv("KAFKA_SASL_USERNAME"),
        password=os.getenv("KAFKA_SASL_PASSWORD"),
    )
    LaunchDarklyClient.connect(sdk_key=os.getenv("LD_SDK_KEY"))
    yield
    # Any code after FastAPI stops / cleanup code?
    await Redis.close()
    await OpenAIClient.close()
    LaunchDarklyClient.close()
    MongoDBClient.close()


app = FastAPI(lifespan=lifespan)


@app.get("/")
async def root():
    return {
        "message": "You've reached the AI chatbot API. Send a POST request to /chat with a question and chat history to get a response."
    }


@app.get("/healthcheck")
async def healthcheck():
    return "ai-members-scheduling Agent is healthy"


@app.post("/v1/chats")
async def query(request: ChatRequest, accept: str = Header("accept")):
    try:
        scratch_pad = request.scratch_pad.model_dump() if request.scratch_pad else {}
        use_stream = accept == "text/event-stream"
        result = await function_dispatch(
            question=request.question,
            history=request.history,
            scratch_pad=scratch_pad,
            starting_function=starting_handler,
            stream=use_stream,
        )
        if use_stream:
            return StreamingResponse(stream_response(result), media_type="text/event-stream")
        return JSONResponse(content=result)
    except Exception as e:
        print(f"Error: {str(e)}")
        print(f"Traceback:\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Internal Server Error")


async def stream_response(result) -> AsyncGenerator[str, None]:
    """Stream response from the agent, yielding tool updates and non-repeated response chunks."""
    past_answer = ""

    try:
        if isinstance(result["answer"], str):
            # If the response is a string, return it as a single event because we missed a function graph node
            delta_event = {"o": "error", "v": result["answer"]}
            yield (f"event: delta\ndata: {json.dumps(delta_event)}\n\n")
        else:
            async for event in result["answer"]:
                if event.choices and event.choices[0].delta:
                    delta = event.choices[0].delta
                    if delta and delta.content:
                        new_delta = delta.content[len(past_answer) :]

                        # Create the delta object separately to avoid f-string issues
                        delta_event = {"o": "append", "v": new_delta}
                        yield (f"event: delta\ndata: {json.dumps(delta_event)}\n\n")

    except Exception as e:
        # Stream an error message if something goes wrong
        error_event = {"o": "show", "v": str(e)}
        yield (f"event: delta\ndata: {json.dumps(error_event)}\n\n")
    if result.get("references"):
        yield (
            f"event: delta\ndata: {json.dumps({'o': 'references', 'v': result['references']})}\n\n"
        )
    if result.get("metadata"):
        yield (f"event: delta\ndata: {json.dumps({'o': 'metadata', 'v': result['metadata']})}\n\n")
    if result.get("path"):
        yield (f"event: delta\ndata: {json.dumps({'o': 'path', 'v': result['path']})}\n\n")
    yield ('event: done\ndata: "DONE"\n\n')


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
