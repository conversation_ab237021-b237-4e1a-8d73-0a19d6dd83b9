#!/usr/bin/env python3
"""
Simple test to check the Scheduling.Bios.Api connection
"""
import asyncio
import os
import httpx
import dotenv

# Load environment variables
dotenv.load_dotenv()

SCHEDULING_BIOS_API_BASE_URL = os.getenv("SCHEDULING_BIOS_API_BASE_URL")
SCHEDULING_BIOS_API_KEY = os.getenv("SCHEDULING_BIOS_API_KEY")


async def test_api_connection():
    """Test the API connection"""
    print(f"Testing API connection to: {SCHEDULING_BIOS_API_BASE_URL}")
    print(f"Using API key: {SCHEDULING_BIOS_API_KEY[:10]}..." if SCHEDULING_BIOS_API_KEY else "No API key")

    if not SCHEDULING_BIOS_API_BASE_URL or not SCHEDULING_BIOS_API_KEY:
        print("ERROR: Missing API configuration!")
        return

    # Try different URL patterns
    urls_to_try = [
        f"{SCHEDULING_BIOS_API_BASE_URL.rstrip('/')}/v2/bios/search",
        f"{SCHEDULING_BIOS_API_BASE_URL.rstrip('/')}/bios/search",
        f"{SCHEDULING_BIOS_API_BASE_URL.rstrip('/')}/v1/bios/search",
        f"{SCHEDULING_BIOS_API_BASE_URL.rstrip('/')}/search",
    ]

    headers = {
        "ocp-apim-subscription-key": SCHEDULING_BIOS_API_KEY,
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    payload = {"query": "Sarah G.", "isPublished": True, "take": 3}

    async with httpx.AsyncClient() as client:
        for search_url in urls_to_try:
            try:
                print(f"\nTrying URL: {search_url}")
                response = await client.post(search_url, headers=headers, json=payload, timeout=15.0)
                print(f"Response status: {response.status_code}")

                if response.status_code == 200:
                    data = response.json()
                    print(f"SUCCESS! Response data: {data}")
                    return
                else:
                    print(f"Error response: {response.text}")

            except Exception as e:
                print(f"Exception occurred: {e}")

        print("\nAll URLs failed. Let's try a GET request to the base URL to see what's available:")
        try:
            base_response = await client.get(SCHEDULING_BIOS_API_BASE_URL, headers={"ocp-apim-subscription-key": SCHEDULING_BIOS_API_KEY})
            print(f"Base URL status: {base_response.status_code}")
            print(f"Base URL response: {base_response.text}")
        except Exception as e:
            print(f"Base URL exception: {e}")


async def main():
    await test_api_connection()


if __name__ == "__main__":
    asyncio.run(main())
