#!/usr/bin/env python3
"""
Simple test script to test the trainer bio functionality directly
"""
import asyncio
import os
import sys
import dotenv

# Load environment variables
dotenv.load_dotenv()

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.repositories.trainer_bio import search_trainer_bio_by_name
from app.handlers.trainer_bio_handler import trainer_bio_handler
from app.handlers.trainer_name_extractor_handler import trainer_name_extractor_handler


async def test_trainer_name_extraction():
    """Test the trainer name extraction"""
    print("Testing trainer name extraction...")
    
    question = "Tell me about <PERSON>."
    history = []
    scratch_pad = {}
    
    try:
        result = await trainer_name_extractor_handler(question, history, scratch_pad)
        print(f"Name extraction result: {result}")
        return result.get('trainer_name_extraction', {}).get('trainer_name')
    except Exception as e:
        print(f"Error in name extraction: {e}")
        return None


async def test_trainer_bio_search():
    """Test the trainer bio search directly"""
    print("Testing trainer bio search...")
    
    trainer_name = "<PERSON>"
    
    try:
        result = await search_trainer_bio_by_name(trainer_name)
        print(f"Bio search result: {result}")
        return result
    except Exception as e:
        print(f"Error in bio search: {e}")
        return None


async def test_trainer_bio_handler():
    """Test the full trainer bio handler"""
    print("Testing trainer bio handler...")
    
    question = "Tell me about Sarah G."
    history = []
    scratch_pad = {}
    extracted_trainer_name = "Sarah G."
    
    try:
        result = await trainer_bio_handler(
            question=question,
            history=history,
            scratch_pad=scratch_pad,
            extracted_trainer_name=extracted_trainer_name,
            stream=False
        )
        print(f"Handler result: {result}")
        return result
    except Exception as e:
        print(f"Error in handler: {e}")
        return None


async def main():
    """Run all tests"""
    print("Starting trainer bio tests...")
    print("=" * 50)
    
    # Test 1: Name extraction
    extracted_name = await test_trainer_name_extraction()
    print("=" * 50)
    
    # Test 2: Bio search
    bio_results = await test_trainer_bio_search()
    print("=" * 50)
    
    # Test 3: Full handler
    handler_result = await test_trainer_bio_handler()
    print("=" * 50)
    
    print("Tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
